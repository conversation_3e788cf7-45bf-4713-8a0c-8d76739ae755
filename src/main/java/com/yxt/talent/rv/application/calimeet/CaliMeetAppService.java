package com.yxt.talent.rv.application.calimeet;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.application.calimeet.dto.*;
import com.yxt.talent.rv.application.todo.CaliMeetSceneStrategy;
import com.yxt.talent.rv.application.todo.TodoSenderComponent;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.result.XpdResultAppService;
import com.yxt.talent.rv.domain.meet.meet.Meet;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CaliMeetAppService {

    private final CalimeetMapper calimeetMapper;
    private final CaliMeetMsgSender caliMeetMsgSender;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final AppProperties appProperties;
    private final CoreAclService coreAclService;
    private final CaliMeetRecordService caliMeetRecordService;
    private final XpdResultAppService xpdResultAppService;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdLevelMapper xpdLevelMapper;

    /**
     * 校准会列表
     */
    public PagingList<CaliMeetPageResDTO> pageCaliMeet(String orgId, CaliMeetPageQueryDTO queryDTO,
            PageRequest pageRequest, String searchKey, List<Integer> caliMeetStatusList, List<String> authUserIds) {
        Page<CalimeetPO> requestPage = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CalimeetPO> pageRes = calimeetMapper.pageQuery(requestPage, orgId, queryDTO.getXpdId(), searchKey,
                caliMeetStatusList, authUserIds);
        PagingList<CalimeetPO> poPagingList = BeanCopierUtil.toPagingList(pageRes);
        if (CollectionUtils.isEmpty(poPagingList.getDatas())) {
            return new PagingList<>(new ArrayList<>(), poPagingList.getPaging());
        }
        PagingList<CaliMeetPageResDTO> res = new PagingList<>();
        res.setPaging(poPagingList.getPaging());
        List<String> caliMeetIds = poPagingList.getDatas().stream().map(CalimeetPO::getId).collect(Collectors.toList());
        List<CalimeetParticipantsPO> participantsPOList = calimeetParticipantsMapper.selectByCaliMeetIds(orgId,
                caliMeetIds);
        Map<String, List<CalimeetParticipantsPO>> caliMeetIdGroup = participantsPOList.stream()
                .collect(Collectors.groupingBy(CalimeetParticipantsPO::getCalimeetId));
        List<CaliMeetPageResDTO> resData = new ArrayList<>();
        List<CaliMeetUserStatisticDTO> meetUserStaticticDTOList = calimeetUserMapper.getCountByMeetIds(orgId,
                caliMeetIds);
        Map<String, Integer> meetUserCOuntMap = meetUserStaticticDTOList.stream()
                .collect(Collectors.toMap(CaliMeetUserStatisticDTO::getMeetId, CaliMeetUserStatisticDTO::getUserCount));
        poPagingList.getDatas().forEach(item -> {
            CaliMeetPageResDTO data = new CaliMeetPageResDTO();
            data.setId(item.getId());
            data.setMeetName(item.getCalimeetName());
            data.setCaliType(item.getCalimeetType());
            data.setMeetStatus(item.getCalimeetStatus());
            data.setMtStartTime(item.getStartTime());
            data.setMtEndTime(item.getEndTime());
            data.setCaliMode(item.getCalimeetMode());
            data.setCreateTime(item.getCreateTime());
            data.setCreateUserId(item.getCreateUserId());
            data.setMeetRecordId(item.getRecord());
            data.setShowRateControl(item.getShowRatio());
            //设置校准人数
            data.setCaliUserCount(meetUserCOuntMap.getOrDefault(item.getId(), 0));
            List<CalimeetParticipantsPO> participants = caliMeetIdGroup.getOrDefault(item.getId(), new ArrayList<>());
            data.setOrganizeUserIds(
                    participants.stream().filter(el -> el.getUserType() == 1).map(CalimeetParticipantsPO::getUserId)
                            .collect(Collectors.toList()));
            data.setCaliUserIds(
                    participants.stream().filter(el -> el.getUserType() == 2).map(CalimeetParticipantsPO::getUserId)
                            .collect(Collectors.toList()));
            resData.add(data);
        });
        res.setDatas(resData);
        return res;
    }

    /**
     * 创建校准会
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String createCaliMeet(String orgId, String xpdId, CaliMeetCreatDTO caliMeetCreatDTO, String optUser) {
        CalimeetPO entity = new CalimeetPO();
        String caliMeetId = ApiUtil.getUuid();
        entity.setId(caliMeetId);
        entity.setCalimeetStatus(0);
        entity.setXpdId(xpdId);
        entity.setCalimeetName(caliMeetCreatDTO.getMeetName());
        entity.setCalimeetType(caliMeetCreatDTO.getMeetType());
        entity.setCalimeetMode(caliMeetCreatDTO.getMeetMode());
        //0-在线校准，1-线下校准
        if (caliMeetCreatDTO.getMeetMode() == 0) {
            entity.setEndTime(caliMeetCreatDTO.getMtEndTime());
        }
        entity.setStartTime(caliMeetCreatDTO.getMtStartTime());
        entity.setShowRatio(caliMeetCreatDTO.getShowRatio());
        entity.setOrgId(orgId);
        entity.setDeleted(0);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateUserId(optUser);
        entity.setUpdateUserId(optUser);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setRecord(caliMeetCreatDTO.getMeetRecordId());
        calimeetMapper.insert(entity);
        //保存校准人和组织人
        List<CalimeetParticipantsPO> participantsPOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(caliMeetCreatDTO.getOrganizeUserIds())) {
            caliMeetCreatDTO.getOrganizeUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 1, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(caliMeetCreatDTO.getCaliUserIds())) {
            caliMeetCreatDTO.getCaliUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 2, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            calimeetParticipantsMapper.batchInsert(participantsPOList);
        }
        return caliMeetId;
    }


    @NotNull
    private CalimeetParticipantsPO getCalimeetParticipantsPO(String caliMeetId, int userType, String userId,
            String orgId, String optUser) {
        CalimeetParticipantsPO participantsPO = new CalimeetParticipantsPO();
        participantsPO.setId(ApiUtil.getUuid());
        participantsPO.setCalimeetId(caliMeetId);
        //干系人类型(1-组织者，2-校准人)
        participantsPO.setUserType(userType);
        participantsPO.setUserId(userId);
        participantsPO.setOrgId(orgId);
        participantsPO.setCaliStatus(0);
        participantsPO.setCreateTime(LocalDateTime.now());
        participantsPO.setCreateUserId(optUser);
        participantsPO.setUpdateTime(LocalDateTime.now());
        participantsPO.setUpdateUserId(optUser);
        participantsPO.setDeleted(0);
        return participantsPO;
    }

    /**
     * 编辑校准会
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void editCaliMeet(String orgId, CaliMeetEditDTO caliMeetEditDTO, String optUser, String token,
            UserCacheDetail userCacheBasic, CalimeetPO calimeetPO) {
        String caliMeetId = calimeetPO.getId();
        String oldMeetName = calimeetPO.getCalimeetName();
        Integer oldMeetType = calimeetPO.getCalimeetType();
        calimeetPO.setCalimeetName(caliMeetEditDTO.getMeetName());
        calimeetPO.setCalimeetType(caliMeetEditDTO.getMeetType());
        calimeetPO.setCalimeetMode(caliMeetEditDTO.getMeetMode());
        //0-在线校准，1-线下校准
        if (caliMeetEditDTO.getMeetMode() == 0) {
            calimeetPO.setEndTime(caliMeetEditDTO.getMtEndTime());
        }
        calimeetPO.setStartTime(caliMeetEditDTO.getMtStartTime());
        calimeetPO.setShowRatio(caliMeetEditDTO.getShowRatio());
        calimeetPO.setUpdateUserId(optUser);
        calimeetPO.setUpdateTime(LocalDateTime.now());
        calimeetMapper.updateById(calimeetPO);
        List<CalimeetParticipantsPO> caliMeetParticipantsOld = calimeetParticipantsMapper.selectByCaliMeetId(orgId,
                caliMeetId);
        List<String> caliUserIdsOld = caliMeetParticipantsOld.stream()
                .filter(el -> Integer.valueOf(2).equals(el.getUserType())).map(CalimeetParticipantsPO::getUserId)
                .collect(Collectors.toList());
        //先删除关系人员
        calimeetParticipantsMapper.deleteByMeetId(orgId, caliMeetId);
        //保存校准人和组织人
        List<CalimeetParticipantsPO> participantsPOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getOrganizeUserIds())) {
            caliMeetEditDTO.getOrganizeUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 1, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.getCaliUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 2, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            calimeetParticipantsMapper.batchInsert(participantsPOList);
        }
        if (Objects.isNull(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.setCaliUserIds(new ArrayList<>());
        }
        //查询哪些校准人被移除发移除消息
        if (CollectionUtils.isNotEmpty(caliUserIdsOld)) {
            List<String> removeUserIds = caliUserIdsOld.stream()
                    .filter(e -> !caliMeetEditDTO.getCaliUserIds().contains(e)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(removeUserIds)) {
                CommonUtil.execAfterCommitIfHas(
                        () -> caliMeetMsgSender.sendTemplateMessagePointUserAsync(token, userCacheBasic,
                                List.of(calimeetPO), 5, removeUserIds));
            }
        }
        //查询哪些校准人新增，发加入消息
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.getCaliUserIds().removeAll(caliUserIdsOld);
            //发消息
            if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
                CommonUtil.execAfterCommitIfHas(
                        () -> caliMeetMsgSender.sendTemplateMessage(token, userCacheBasic, List.of(calimeetPO), 1));
            }
        }
        if (!caliMeetEditDTO.getMeetName().equals(oldMeetName)) {
            // 如果校准会名称发生变更，待办名称修改
            CommonUtil.execAfterCommitIfHas(() -> modifyCaliMeetTodo(orgId, optUser, calimeetPO));
        }

        if (!caliMeetEditDTO.getMeetType().equals(oldMeetType)) {
            // 如果校准会名称发生变更，待办名称修改
            CommonUtil.execAfterCommitIfHas(() -> caliMeetRecordService.delCaliMeetRecord(orgId, caliMeetId));
        }

    }


    /**
     * 校准会基本信息查询
     */
    @NotNull
    public CaliMeetBaseInfoDTO detailCaliMeet(String orgId, String caliMeetId) {
        if (StringUtils.isBlank(caliMeetId)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        CaliMeetBaseInfoDTO res = new CaliMeetBaseInfoDTO();
        res.setMeetName(calimeetPO.getCalimeetName());
        res.setMeetMode(calimeetPO.getCalimeetMode());
        res.setMeetType(calimeetPO.getCalimeetType());
        res.setShowRatio(calimeetPO.getShowRatio());
        res.setMtStartTime(calimeetPO.getStartTime());
        res.setMtEndTime(calimeetPO.getEndTime());
        res.setId(calimeetPO.getId());
        res.setMeetRecordId(calimeetPO.getRecord());
        res.setMeetStatus(calimeetPO.getCalimeetStatus());
        List<CalimeetParticipantsPO> participantsPOList = calimeetParticipantsMapper.selectByCaliMeetId(orgId,
                caliMeetId);
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            List<CalimeetParticipantsPO> orgUsers = participantsPOList.stream().filter(el -> el.getUserType() == 1)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orgUsers)) {
                res.setOrganizeUserIds(orgUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()));
            }
            List<CalimeetParticipantsPO> caliUsers = participantsPOList.stream().filter(el -> el.getUserType() == 2)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caliUsers)) {
                res.setCaliUserIds(caliUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()));
            }
        }
        return res;
    }

    /**
     * 删除校准会
     *
     * @param id     校准会id
     * @param userId 用户id
     * @param orgId  组织id
     */
    public void deleteMeeting(String id, String userId, String orgId) {
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        meetEntity.setDeleted(DeleteEnum.DELETED.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);
    }


    /**
     * 结束校准会
     *
     * @param calimeetId     校准会id
     * @param userId 用户id
     * @param orgId  组织id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void closeMeeting(String calimeetId, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, calimeetId);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        if (calimeet == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        calimeet.setCalimeetStatus(Meet.CaliMeetStatusEnum.FINISHED.getCode());
        EntityUtil.setUpdate(calimeet, userId);
        calimeetMapper.updateById(calimeet);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(calimeet), 4));

        // 数据同步
        //syncCalibrationData(cmEntity, userId);

        syncCalimeetData(calimeet, userId, orgId);

    }

    /**
     * 将校准结果同步到盘点项目中的员工结果
     * @param calimeet
     * @param userId
     * @param orgId
     */
    private void syncCalimeetData(CalimeetPO calimeet, String userId, String orgId) {
        String xpdId = calimeet.getXpdId();
        String calimeetId = calimeet.getId();
        log.info("LOG21493:开始同步校准会数据到盘点项目，calimeetId={}, xpdId={}, orgId={}", calimeet.getId(), xpdId, orgId);

        // 2. 查询校准会的所有被校准人的结果
        List<CalimeetDimResultDto> calimeetRecords = calimeetRecordMapper.getAllRecordsByCalimeetId(orgId, calimeetId);
        if (CollectionUtils.isEmpty(calimeetRecords)) {
            log.info("校准会没有校准记录，跳过同步，calimeetId={}", calimeetId);
            return;
        }

        log.info("LOG21503:找到{}条校准记录需要同步", calimeetRecords.size());

        // 3. 同步suggestion到XpdUserExtPO
        syncSuggestionToXpdUserExt(orgId, xpdId, calimeetRecords, userId);

        // 4. 同步到xpd结果表：XpdResultUserPO, XpdResultUserDimPO, XpdResultUserDimcombPO, XpdResultUserIndicatorPO
        syncCalimeetResultsToXpd(orgId, xpdId, calimeetRecords, userId, calimeetId);

        log.info("LOG21513:校准会数据同步完成，calimeetId={}", calimeetId);
    }

    /**
     * 同步发展建议到XpdUserExtPO
     */
    private void syncSuggestionToXpdUserExt(String orgId, String xpdId, List<CalimeetDimResultDto> calimeetRecords, String operatorId) {
        log.info("LOG21483:开始同步发展建议到XpdUserExt表，记录数量={}", calimeetRecords.size());

        List<String> userIds = calimeetRecords.stream()
            .map(CalimeetDimResultDto::getUserId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            log.info("没有有效的用户ID，跳过同步发展建议");
            return;
        }

        // 查询现有的XpdUserExt记录
        List<XpdUserExtPO> existingRecords = xpdUserExtMapper.selectByXpdIdAndUserIds(orgId, xpdId, userIds);
        Map<String, XpdUserExtPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdUserExtPO::getUserId, Function.identity()));

        // 构建校准记录映射
        Map<String, String> suggestionMap = calimeetRecords.stream()
            .filter(record -> StringUtils.isNotBlank(record.getUserId()))
            .collect(Collectors.toMap(
                CalimeetDimResultDto::getUserId,
                record -> StringUtils.defaultString(record.getSuggestion(), ""),
                (existing, replacement) -> replacement
            ));

        List<XpdUserExtPO> toUpdate = new ArrayList<>();
        List<XpdUserExtPO> toInsert = new ArrayList<>();

        for (String userId : userIds) {
            String suggestion = suggestionMap.get(userId);
            XpdUserExtPO existing = existingMap.get(userId);

            if (existing != null) {
                // 更新现有记录
                existing.setSuggestion(suggestion);
                existing.setUpdateUserId(operatorId);
                existing.setUpdateTime(new Date());
                toUpdate.add(existing);
            } else {
                // 创建新记录
                XpdUserExtPO newRecord = new XpdUserExtPO();
                newRecord.setId(ApiUtil.getUuid());
                newRecord.setOrgId(orgId);
                newRecord.setXpdId(xpdId);
                newRecord.setUserId(userId);
                newRecord.setSuggestion(suggestion);
                newRecord.setDeleted(0);
                EntityUtil.setAuditFields(newRecord, operatorId);
                toInsert.add(newRecord);
            }
        }

        // 批量更新
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            xpdUserExtMapper.batchUpdateRvXpdUserExt(toUpdate);
            log.info("LOG21523:更新了{}条XpdUserExt记录的发展建议", toUpdate.size());
        }

        // 批量插入
        if (CollectionUtils.isNotEmpty(toInsert)) {
            xpdUserExtMapper.insertBatch(toInsert);
            log.info("LOG21533:插入了{}条新的XpdUserExt记录", toInsert.size());
        }
    }

    /**
     * 同步校准结果到XPD结果表
     */
    private void syncCalimeetResultsToXpd(String orgId, String xpdId, List<CalimeetDimResultDto> calimeetRecords, String operatorId, String calimeetId) {
        log.info("开始同步校准结果到XPD结果表，记录数量={}", calimeetRecords.size());

        // 获取校准会信息以确定校准类型
        if (CollectionUtils.isEmpty(calimeetRecords)) {
            return;
        }

        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        if (calimeet == null) {
            log.warn("无法获取校准会信息，跳过同步");
            return;
        }

        int calimeetType = calimeet.getCalimeetType(); // 校准会类型：0-维度分层结果，1-维度结果，2-指标结果

        for (CalimeetDimResultDto record : calimeetRecords) {
            if (StringUtils.isBlank(record.getUserId())) {
                continue;
            }

            try {
                // 解析校准详情
                CaliUpdateUserResultWrapDto caliDetails = parseCalimeetDetails(record.getCaliDetails());
                if (caliDetails == null || CollectionUtils.isEmpty(caliDetails.getUserResults())) {
                    log.debug("LOG21553:用户{}没有校准详情数据，跳过", record.getUserId());
                    continue;
                }
                CaliDimResultWrapDto resultDetails = parseCalimeetResults(record.getResultDetails());

                // 用户校准之后，重新计算的盘点项目结果， 维度结果，指标结果等数据，如果某个维度结果经过校准之后没有计算出来任务结果，这里不会存储，复制的时候也需要清理掉原有的校准结果
                List<CaliUpdateUserResultDto> userResults = caliDetails.getUserResults();

                // 根据校准会类型同步不同的结果表 (CaliMeetTypeEnum)
                switch (calimeetType) {
                    case 0: // CaliMeetTypeEnum.LEVEL - 维度分层结果
                        syncUserDimLevelResults(orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    case 1: // CaliMeetTypeEnum.DIM - 维度结果
                        syncUserDimResults(orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    case 2: // CaliMeetTypeEnum.INDICATOR - 指标结果
                        syncUserIndicatorResults(orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    default:
                        log.warn("未知的校准会类型：{}", calimeetType);
                }

            } catch (Exception e) {
                log.error("同步用户{}的校准结果时发生异常", record.getUserId(), e);
            }
        }

        log.info("校准结果同步到XPD结果表完成");
    }

    private CaliDimResultWrapDto parseCalimeetResults(String resultDetails) {
        if (StringUtils.isBlank(resultDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(resultDetails, CaliDimResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21563:解析校准结果JSON失败：{}", resultDetails, e);
            return null;
        }
    }


    /**
     * 解析校准详情JSON
     */
    @jakarta.annotation.Nullable
    private CaliUpdateUserResultWrapDto parseCalimeetDetails(String caliDetails) {
        if (StringUtils.isBlank(caliDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(caliDetails, CaliUpdateUserResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21543:解析校准详情JSON失败：{}", caliDetails, e);
            return null;
        }
    }

    /**
     * 同步用户维度分层结果
     */
    private void syncUserDimLevelResults(
            String orgId, String xpdId, String userId,
            List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails, String operatorId) {
        log.debug("LOG21573:同步用户{}的维度分层结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户维度结果
     */
    private void syncUserDimResults(String orgId, String xpdId, String userId,
                                  List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails, String operatorId) {
        log.debug("同步用户{}的维度结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户指标结果
     */
    private void syncUserIndicatorResults(
            String orgId, String xpdId, String userId,
            List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails,
            String operatorId) {
        log.debug("同步用户{}的指标结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步指标结果（先清理后同步）
        syncCompleteUserIndicatorResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户项目级别结果
     */
    private void syncUserProjectResult(String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        if (resultDetails == null || StringUtils.isBlank(resultDetails.getXpdLevelId())) {
            log.debug("用户{}没有项目级别结果数据，跳过同步", userId);
            return;
        }

        // 查询现有的用户项目结果记录
        XpdResultUserPO existingResult = xpdResultUserMapper.findByUserId(orgId, xpdId, userId);

        if (existingResult != null) {
            // 更新现有记录
            existingResult.setXpdLevelId(resultDetails.getXpdLevelId());
            existingResult.setUpdateUserId(operatorId);
            existingResult.setUpdateTime(LocalDateTime.now());
            existingResult.setCaliFlag(1); // 标记为已校准
            // 原始快照
            existingResult.buildSnapshot();
            xpdResultUserMapper.updateByPrimaryKey(existingResult);
            log.debug("LOG21613:更新了用户{}的项目级别结果，等级ID={}", userId, resultDetails.getXpdLevelId());
        } else {
            log.debug("LOG21603:未找到用户{}的项目级别结果记录，生成一个新的", userId);
            XpdResultUserPO po = new XpdResultUserPO();
            po.setOrgId(orgId);
            po.setXpdId(xpdId);
            po.setUserId(userId);
            po.setXpdLevelId(resultDetails.getXpdLevelId());
            po.setCompetent(isCompetent(orgId, xpdId, resultDetails.getXpdLevelId()));
            po.setUpdateUserId(operatorId);
            po.setUpdateTime(LocalDateTime.now());
            po.setCaliFlag(1); // 标记为已校准
            po.setOriginalSnap(null); // 新增的记录之前没有盘点结果，无法记录快照
            xpdResultUserMapper.insert(po);
        }
    }

    /**
     * 判断用户是否达标
     */
    private Integer isCompetent(String orgId, String xpdId, String xpdLevelId) {
        List<XpdLevelPO> xpdLevels = xpdLevelMapper.selectByXpdId(orgId, xpdId);
        for (XpdLevelPO xpdLevel : xpdLevels) {
            if (xpdLevel.getId().equals(xpdLevelId)) {
                return xpdLevel.getCompetent();
            }
        }
        log.debug("LOG21593:{},{},{}", orgId, xpdId, xpdLevelId);
        return 0;
    }



    /**
     * 启动校准会
     *
     * @param id       校准会id
     * @param userId   用户id
     * @param orgId    组织id
     * @param operator 用户信息
     * @param token    token
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void openMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        meetEntity.setCalimeetStatus(Meet.CaliMeetStatusEnum.UNDERWAY.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 1));
        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 2));
        // 发送待办
        CommonUtil.execAfterCommitIfHas(() -> addCaliMeetTodo(orgId, operator.getUserId(), meetEntity, null));
    }

    /**
     * 完整同步用户维度结果（先清理后同步）
     */
    private void syncCompleteUserDimResults(String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        if (resultDetails == null) {
            log.debug("用户{}没有结果详情数据，跳过维度结果同步", userId);
            return;
        }

        // 1. 查询用户现有的所有维度结果
        List<XpdResultUserDimPO> existingRecords = xpdResultUserDimMapper.findByXpdIdAndUserId(orgId, xpdId, userId);
        Map<String, XpdResultUserDimPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdResultUserDimPO::getSdDimId, Function.identity()));

        // 2. 从resultDetails中获取应该保留的维度ID
        Set<String> targetDimIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            targetDimIds = resultDetails.getDimResults().stream()
                .map(CaliDimResultDto::getSdDimId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        }
        final Set<String> effectivelyFinalTargetDimIds = targetDimIds;

        // 3. 清理不在resultDetails中的旧维度结果
        List<String> toDeleteIds = existingRecords.stream()
            .filter(record -> !effectivelyFinalTargetDimIds.contains(record.getSdDimId()))
            .map(XpdResultUserDimPO::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toDeleteIds)) {
            // 逻辑删除不在resultDetails中的维度结果
            List<XpdResultUserDimPO> toDelete = toDeleteIds.stream()
                .map(id -> {
                    XpdResultUserDimPO po = new XpdResultUserDimPO();
                    po.setId(id);
                    po.setDeleted(1);
                    po.setUpdateUserId(operatorId);
                    po.setUpdateTime(LocalDateTime.now());
                    return po;
                })
                .collect(Collectors.toList());
            xpdResultUserDimMapper.batchUpdateResult(toDelete);
            log.debug("LOG21623:清理了用户{}的旧维度:{}", userId, toDeleteIds);
        }

        // 4. 同步resultDetails中的维度结果
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            List<XpdResultUserDimPO> toUpdate = new ArrayList<>();

            for (CaliDimResultDto dimResult : resultDetails.getDimResults()) {
                if (StringUtils.isBlank(dimResult.getSdDimId())) {
                    continue;
                }

                XpdResultUserDimPO existing = existingMap.get(dimResult.getSdDimId());
                if (existing != null && existing.getDeleted() == 0) {
                    // 更新现有记录
                    existing.buildSnapshot(); // 保存原始快照
                    if (StringUtils.isNotBlank(dimResult.getGridLevelId())) {
                        existing.setGridLevelId(dimResult.getGridLevelId());
                    }
                    if (dimResult.getScoreValue() != null) {
                        existing.setScoreValue(dimResult.getScoreValue());
                    }
                    if (dimResult.getQualifiedPtg() != null) {
                        existing.setQualifiedPtg(dimResult.getQualifiedPtg());
                    }
                    existing.setUpdateUserId(operatorId);
                    existing.setUpdateTime(LocalDateTime.now());
                    existing.setCaliFlag(1); // 标记为已校准
                    toUpdate.add(existing);
                }
            }

            if (CollectionUtils.isNotEmpty(toUpdate)) {
                xpdResultUserDimMapper.batchUpdateResult(toUpdate);
                log.debug("LOG21633:更新了用户{}的{}个维度结果", userId, toUpdate.size());
            }
        }
    }

    /**
     * 完整同步用户指标结果（先清理后同步）
     */
    private void syncCompleteUserIndicatorResults(String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        // 注意：指标结果通常不在CaliDimResultWrapDto中，而是在caliDetails中
        // 这里主要是为了保持接口一致性，实际的指标同步逻辑可能需要从其他地方获取数据
        log.debug("用户{}的指标结果同步（当前版本暂不处理指标结果的完整同步）", userId);

        // TODO: 如果需要处理指标结果的完整同步，需要：
        // 1. 查询用户现有的所有指标结果
        // 2. 从校准结果中获取应该保留的指标ID
        // 3. 清理不在校准结果中的旧指标结果
        // 4. 同步校准结果中的指标结果
    }

    /**
     * 同步用户维度组结果
     */
    private void syncUserDimCombResults(String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        if (resultDetails == null || CollectionUtils.isEmpty(resultDetails.getDimResults())) {
            log.debug("LOG21643:用户{}没有维度结果数据，跳过维度组结果同步", userId);
            return;
        }

        // 1. 查询项目的所有维度组
        List<XpdDimCombPO> dimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(dimCombs)) {
            log.debug("LOG21653:项目{}没有维度组配置，跳过维度组结果同步", xpdId);
            return;
        }

        // 2. 查询用户现有的维度组结果
        List<XpdResultUserDimcombPO> existingCombResults = xpdResultUserDimcombMapper.findByXpdIdAndUserIds(
            orgId, xpdId, Collections.singletonList(userId)
        );

        // 3. 清理用户的所有维度组结果（因为维度组结果需要重新计算）
        if (CollectionUtils.isNotEmpty(existingCombResults)) {
            xpdResultUserDimcombMapper.removeByXpdId(orgId, xpdId, Collections.singletonList(userId));
            log.debug("LOG21663:清理了用户{}的{}个维度组结果", userId, existingCombResults.size());
        }

        // 4. 重新计算并同步维度组结果
        // 这里需要根据维度结果重新计算用户在各个维度组中的落位
        // 具体的计算逻辑需要参考XpdResultCalcService中的相关方法
        log.debug("用户{}的维度组结果重新计算完成（具体计算逻辑待实现）", userId);

        // TODO: 实现维度组结果的重新计算和同步
        // 1. 遍历每个维度组
        // 2. 根据X轴和Y轴维度的结果计算用户在该维度组中的落位
        // 3. 创建新的维度组结果记录
    }

    /**
     * 撤回校准会
     *
     * @param id       校准会id
     * @param userId   用户id
     * @param orgId    组织id
     * @param operator 用户信息
     * @param token    token
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void withdrawMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 校验是否可以撤回
        if (meetEntity.getCalimeetStatus() != Meet.CaliMeetStatusEnum.UNDERWAY.getCode()) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        meetEntity.setCalimeetStatus(Meet.CaliMeetStatusEnum.DEFAULT.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 3));
        // 删除待办
        CommonUtil.execAfterCommitIfHas(() -> deleteTodoByUIds(orgId, operator.getUserId(), ListUtil.toList(id)));
    }


    /**
     * 添加校准会待办
     *
     * @param orgId         组织id
     * @param opUserId      操作人id
     * @param calimeet      校准会信息
     * @param caliMeetUsers 校准会参与者列表
     */
    private void addCaliMeetTodo(String orgId, String opUserId, CalimeetPO calimeet,
            List<CalimeetParticipantsPO> caliMeetUsers) {
        if (CollectionUtils.isNotEmpty(caliMeetUsers)) {
            // CaliMeetTodoInfoDto
            CaliMeetSceneStrategy.CaliMeetTodoInfoDto caliMeetTodoInfoDto = new CaliMeetSceneStrategy.CaliMeetTodoInfoDto();
            // 设置caliMeetUsers列表
            caliMeetTodoInfoDto.setCaliMeetUsers(caliMeetUsers);
            // 设置calimeet
            caliMeetTodoInfoDto.setMeetName(calimeet.getCalimeetName());
            caliMeetTodoInfoDto.setStartTime(calimeet.getStartTime());
            caliMeetTodoInfoDto.setEndTime(calimeet.getEndTime());

            String url = coreAclService.getScanentryURL(orgId, appProperties.getCaliMeetMsgUrl(), "");
            // 设置跳转url
            caliMeetTodoInfoDto.setUrl(url);

            try {
                TodoSenderComponent.getCaliMeetSceneStrategy().createTodos(orgId, opUserId, caliMeetTodoInfoDto);
            } catch (Exception e) {
                log.error("待办异常 addCaliMeetTodo error. first:{}", JSON.toJSONString(caliMeetUsers.get(0)), e);
            }
        }
    }

    private void modifyCaliMeetTodo(String orgId, String opUserId, CalimeetPO calimeet) {
        // CaliMeetTodoInfoDto
        CaliMeetSceneStrategy.CaliMeetTodoInfoDto caliMeetTodoInfoDto = new CaliMeetSceneStrategy.CaliMeetTodoInfoDto();

        // 设置calimeet
        caliMeetTodoInfoDto.setMeetName(calimeet.getCalimeetName());
        caliMeetTodoInfoDto.setStartTime(calimeet.getStartTime());
        caliMeetTodoInfoDto.setEndTime(calimeet.getEndTime());

        String url = coreAclService.getScanentryURL(orgId, appProperties.getCaliMeetMsgUrl(), "");
        // 设置跳转url
        caliMeetTodoInfoDto.setUrl(url);
        try {
            TodoSenderComponent.getCaliMeetSceneStrategy().modifyTodoInfo(orgId, opUserId, caliMeetTodoInfoDto);
        } catch (Exception e) {
            log.error("待办异常 modifyCaliMeetTodo error ", e);
        }
    }

    /**
     * 删除校准会待办
     *
     * @param orgId    组织id
     * @param opUserId 操作人id
     * @param meetIds  校准会id列表
     */
    private void deleteTodoByUIds(String orgId, String opUserId, List<String> meetIds) {
        if (CollectionUtils.isNotEmpty(meetIds)) {
            // 调用deleteTodos方法
            try {
                TodoSenderComponent.getCaliMeetSceneStrategy().deleteTodos(orgId, opUserId,
                        meetIds.stream().map(String::valueOf).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("撤回校准会清空所有待办 deleteTodoByIds error. first:{}", JSON.toJSONString(meetIds.get(0)),
                        e);
            }
        }
    }

    /**
     * 删除盘点项目的校准会
     *
     * @param orgId      机构id
     * @param xpdId      项目id
     * @param operatorId 操作人
     * @return
     */
    public void deleteMeetingByXpdId(String orgId, String xpdId, String operatorId) {
        calimeetMapper.deleteMeetingByXpdId(orgId, xpdId, operatorId);
    }

    public List<CalimeetPO> getCaliMeetByXpdId(String orgId, String xpdId) {
        return calimeetMapper.selectListByXpdIdAndOrgId(xpdId, orgId);
    }

    @NotNull
    public CalimeetPO getCaliMeet(String orgId, String caliMeetId) {
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (Objects.isNull(calimeetPO)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        return calimeetPO;
    }
}
